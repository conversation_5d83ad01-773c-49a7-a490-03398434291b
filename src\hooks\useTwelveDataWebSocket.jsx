import { useEffect, useRef, useState } from 'react';

function useTwelveDataWebSocket(symbol, apiKey) {
  const [data, setData] = useState(null);
  const [status, setStatus] = useState('connecting'); // 'connecting' | 'open' | 'closed' | 'error'
  const ws = useRef(null);
  const heartbeatInterval = useRef(null);

  useEffect(() => {
    if (!symbol || !apiKey) {
      console.warn('WebSocket hook: symbol or apiKey missing');
      return;
    }

    const wsUrl = `wss://ws.twelvedata.com/v1/quotes/price?apikey=${apiKey}`;
    console.log(`[WebSocket] Connecting to ${wsUrl}`);

    ws.current = new WebSocket(wsUrl);

    ws.current.onopen = () => {
      console.log(`[WebSocket] Connection opened`);

      setStatus('open');

      const subscribeMsg = {
        action: 'subscribe',
        params: { symbols: symbol.toUpperCase() },
      };

      console.log(`[WebSocket] Subscribing to symbol: ${symbol.toUpperCase()}`);
      ws.current.send(JSON.stringify(subscribeMsg));

      heartbeatInterval.current = setInterval(() => {
        const heartbeat = JSON.stringify({ action: 'heartbeat' });
        console.log('[WebSocket] Sending heartbeat');
        ws.current.send(heartbeat);
      }, 10000);
    };

    ws.current.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('[WebSocket] Message received:', message);

        if (message.event === 'price') {
          setData(message);
        } else if (message.event === 'subscribe-status') {
          console.log('[WebSocket] Subscription status:', message);
        } else {
          console.log('[WebSocket] Other message:', message);
        }
      } catch (err) {
        console.error('[WebSocket] Failed to parse message:', event.data);
      }
    };

    ws.current.onerror = (error) => {
      console.error('[WebSocket] Error occurred:', error);
      setStatus('error');
    };

    ws.current.onclose = (event) => {
      console.log('[WebSocket] Connection closed:', event);
      setStatus('closed');

      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
      }
    };

    return () => {
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
      }

      if (ws.current && ws.current.readyState === WebSocket.OPEN) {
        const unsubscribeMsg = {
          action: 'unsubscribe',
          params: { symbols: symbol.toUpperCase() },
        };

        console.log(`[WebSocket] Unsubscribing from symbol: ${symbol.toUpperCase()}`);
        ws.current.send(JSON.stringify(unsubscribeMsg));

        console.log('[WebSocket] Closing connection');
        ws.current.close();
      } else {
        console.log('[WebSocket] Cleanup: no active connection to close');
      }
    };
  }, [symbol, apiKey]);

  return { data, status };
}

export default useTwelveDataWebSocket;
