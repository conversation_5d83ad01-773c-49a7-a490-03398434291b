import React from "react";
import { Table, Checkbox } from "antd";

export default function Earnings({ data, selectedRowKeys, onChange }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "company" },
    { title: "EPS TTM", dataIndex: "eps" },
    {
      title: "Rev. Growth TTM",
      dataIndex: "revGrowth",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
    {
      title: "Profit Growth TTM",
      dataIndex: "profitGrowth",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
  ];

  return <Table columns={columns} dataSource={data} pagination={false} />;
}
