import React, { useState, useEffect } from 'react';
import { Modal, Checkbox } from 'antd';
import { filterCategories } from './FiltersData';
import { useDispatch, useSelector } from 'react-redux';
import { addFilter, clearFilters } from '../../redux/filter/filterSlice';

const FilterModal = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const selectedFilters = useSelector((state) => state.filters.selectedFilters);
  const [localSelected, setLocalSelected] = useState([]);

  // Sync local selection when modal opens
  useEffect(() => {
    if (visible) {
      setLocalSelected(selectedFilters);
    }
  }, [visible, selectedFilters]);

  const handleOk = () => {
    dispatch(clearFilters());
    localSelected.forEach((f) => dispatch(addFilter(f)));
    onClose();
  };

  return (
    <Modal
      title="Select Filters"
      open={visible}           // ✅ updated from `visible` to `open`
      onOk={handleOk}
      onCancel={onClose}
      width={800}
      destroyOnClose
    >
      {filterCategories.map((category) => (
        <div key={category.title} className="mb-4">
          <h3 className="font-semibold mb-2">{category.title}</h3>
          <Checkbox.Group
            options={category.filters}
            value={localSelected}
            onChange={setLocalSelected}
          />
        </div>
      ))}
    </Modal>
  );
};

export default FilterModal;
