// /src/redux/user/userService.js
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const backendURL = import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({
    baseUrl: backendURL,
    credentials: "include", // include cookies
    prepareHeaders: (headers, { getState }) => {
      // With cookies, we no longer use tokens from state.
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getUserDetails: builder.query({
      query: () => ({ url: "/api/protected/me", method: "GET" }),
    }),
  }),
});

export const { useGetUserDetailsQuery } = authApi;
