
// import { createChart, ColorType, CandlestickSeries, HistogramSeries } from 'lightweight-charts';
// import { useEffect, useRef } from 'react';

// const StockChart = ({ data }) => {
//     const chartContainerRef = useRef();

//     useEffect(() => {
//         const chartOptions = {
//             layout: {
//                 textColor: 'black',
//                 background: { type: ColorType.Solid, color: 'white' },
//             },
//         };

//         const chart = createChart(chartContainerRef.current, chartOptions);

//         // Candlestick series with adjusted scale margins
//         const candlestickSeries = chart.addSeries(CandlestickSeries, {
//             upColor: '#26a69a',
//             downColor: '#ef5350',
//             borderVisible: false,
//             wickUpColor: '#26a69a',
//             wickDownColor: '#ef5350',
//         });

//         candlestickSeries.priceScale().applyOptions({
//             scaleMargins: {
//                 top: 0.1,  // Keep some space at the top
//                 bottom: 0.3, // Ensure space for volume bars
//             },
//         });

//         // Volume series positioned at the bottom
//         const volumeSeries = chart.addSeries(HistogramSeries, {
//             color: '#888', 
//             priceFormat: { type: 'volume' }, 
//             priceScaleId: '', // Overlay the volume bars
//         });

//         volumeSeries.priceScale().applyOptions({
//             scaleMargins: {
//                 top: 0.9, // Ensures volume bars stay below candlesticks
//                 bottom: 0, // Aligns volume bars to the bottom
//             },
//         });

//         candlestickSeries.setData(data.map(({ open, high, low, close, time }) => ({ open, high, low, close, time })));

//         volumeSeries.setData(data.map(({ time, volume, open, close }) => ({
//             time,
//             value: volume,
//             color: close >= open ? '#26a69a' : '#ef5350',
//         })));

//         chart.timeScale().fitContent();

//         return () => chart.remove();
//     }, [data]);

//     return <div ref={chartContainerRef} style={{ width: '100%', height: '500px' }} />;
// };

// export default StockChart;



// // import { useParams } from "react-router-dom";
import useTwelveDataWebSocket from '../../hooks/useTwelveDataWebSocket';

const StockChart = () => {
//     const { symbol } = useParams();
// console.log(symbol)
const symbol = "BTC/USD"

const apiKey = '5688324e509d4f84b4e03cad2f60bf27'; // ideally from env or config
const { latestPrice, status, error } = useTwelveDataWebSocket(symbol,apiKey);

if (status === 'connecting') return <div>Connecting...</div>;
if (status === 'error') return <div>Error connecting to WebSocket</div>;
  return (
    <div className="p-4 border rounded shadow w-64">
      <h2 className="text-xl font-bold">{symbol} Live Price</h2>
      {status === 'connecting' && <p>Connecting to WebSocket...</p>}
      {status === 'error' && <p className="text-red-600">Error: {error}</p>}
      {status === 'connected' && !latestPrice && <p>Waiting for data...</p>}
      {latestPrice && <p className="text-green-600 text-lg font-semibold">${latestPrice}</p>}
    </div>
  )
}

export default StockChart