import React from "react";
import PropTypes from "prop-types";
import { But<PERSON> } from "antd";
import { EditOutlined } from "@ant-design/icons";

const StockDetailsCard = ({ metrics }) => {
  return (
    <div className="w-full sm:w-2/3 lg:w-1/3 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      {/* Header with Title & Edit Button */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white">
          Stock Details
        </h2>
        <Button type="default" icon={<EditOutlined />}>
          Edit
        </Button>
      </div>

      {/* Stock Metrics Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
        {metrics.map(({ id, label, value }) => (
          <div
            key={id}
            className="flex justify-between items-center p-2 border-b border-gray-200 dark:border-gray-700"
          >
            <span className="font-semibold text-gray-900 dark:text-white">
              {label}
            </span>
            <span className="text-gray-600 dark:text-gray-300">{value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

StockDetailsCard.propTypes = {
  metrics: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    })
  ).isRequired,
};

export default StockDetailsCard;