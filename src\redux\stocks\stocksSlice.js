import { createSlice } from "@reduxjs/toolkit";
import {
  fetchStocks,
  fetchStockHeaderBySymbol,
  getTimeSeriesData,
  fetchQuotesBySymbols,
  fetchTasi,
  fetchNumo,
  fetchAllMarket,
} from "./stocksActions";

const initialState = {
  data: [],
  header: null,
  timeSeriesData: null,
  quotes: [],
  status: "idle",
  error: null,
  loading: false,
  // quotes: [],
  quotesLoading: false,
  tasi: null, // New field for TASI index
  numo: null, // New field for NUMO index
  allMarket: [], // Optional: Store all market data separately if needed
};

const stocksSlice = createSlice({
  name: "stocks",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Stocks list
      .addCase(fetchStocks.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchStocks.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchStocks.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      })

      // Header from backend
      .addCase(fetchStockHeaderBySymbol.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchStockHeaderBySymbol.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.header = action.payload;
      })
      .addCase(fetchStockHeaderBySymbol.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // Time series
      .addCase(getTimeSeriesData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTimeSeriesData.fulfilled, (state, action) => {
        state.loading = false;
        state.timeSeriesData = action.payload;
      })
      .addCase(getTimeSeriesData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Live quotes
      .addCase(fetchQuotesBySymbols.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuotesBySymbols.fulfilled, (state, action) => {
        state.loading = false;
        action.payload.forEach((q) => {
          const idx = state.data.findIndex((s) => s.symbol === q.symbol);
          if (idx !== -1) {
            state.data[idx] = { ...state.data[idx], ...q };
          }
        });
      })
      .addCase(fetchQuotesBySymbols.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
        // TASI
      .addCase(fetchTasi.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchTasi.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.tasi = action.payload; // Store TASI data separately
      })
      .addCase(fetchTasi.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      })

      // NUMO
      .addCase(fetchNumo.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchNumo.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.numo = action.payload; // Store NUMO data separately
      })
      .addCase(fetchNumo.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      })

      // All Market
      .addCase(fetchAllMarket.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchAllMarket.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.allMarket = action.payload; // Store all market data separately
      })
      .addCase(fetchAllMarket.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });

    // TASI / NOMU / All-market
    // [fetchTasi, fetchNumo, fetchAllMarket].forEach((thunk) => {
    //   builder
    //     .addCase(thunk.pending, (state) => {
    //       state.status = "loading";
    //     })
    //     .addCase(thunk.fulfilled, (state, action) => {
    //       state.status = "succeeded";
    //       state.data = action.payload;
    //     })
    //     .addCase(thunk.rejected, (state, action) => {
    //       state.status = "failed";
    //       state.error = action.error.message;
    //     });
    // });
  },
});

export default stocksSlice.reducer;
