import { Table, Checkbox, Button } from "antd";

const stockData = [
  {
    key: "1",
    symbol: "2222",
    company: "Saudi Aramco Base Oil Co.",
    price: "25SAR",
    dayChange: "-2.5%",
    divYield: "3.5%",
    peRatio: "15x",
    high52: "30SAR",
    low52: "22SAR",
    marketCap: "3.5T SAR",
  },
];

const StocksTable = () => {
  const columns = [
    {
      title: "Pin",
      dataIndex: "select",
      render: () => <Checkbox />,
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "company" },
    { title: "Price", dataIndex: "price" },
    {
      title: "1D Change",
      dataIndex: "dayChange",
      render: (text) => (
        <span className={text.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {text}
        </span>
      ),
    },
    { title: "Div. Yield", dataIndex: "divYield" },
    { title: "PE Ratio", dataIndex: "peRatio" },
    { title: "52 High", dataIndex: "high52" },
    { title: "52 Low", dataIndex: "low52" },
    { title: "Market Cap", dataIndex: "marketCap" },
  ];

  return (
    <div className="p-4 rounded-lg shadow-md bg-white dark:bg-gray-800 dark:text-white">
      <div className="flex justify-between items-center mb-2 px-2">
        <h2 className="text-lg font-semibold bg-white dark:bg-gray-700 px-2 py-1 rounded-md dark:text-white">
          Industry
        </h2>
        <Button className="border px-3 py-1 text-black dark:text-white dark:border-white">
          ✏️ Edit
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={stockData}
        pagination={false}
        bordered
        className="bg-white dark:bg-gray-800"
      />
    </div>
  );
};

export default StocksTable;
