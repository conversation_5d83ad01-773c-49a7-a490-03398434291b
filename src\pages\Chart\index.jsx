import MainContentHeader from "../../components/common/MainContentHeader"

import { Search } from 'lucide-react';
import StockChart from "./StockChart";
import SearchBox from "../../components/common/SearchBox";
const dummyData = [
  { open: 10, high: 10.63, low: 9.49, close: 9.55, volume: 1200, time: 1642427876 },
  { open: 9.55, high: 10.30, low: 9.42, close: 9.94, volume: 1800, time: 1642514276 },
  { open: 9.94, high: 10.17, low: 9.92, close: 9.78, volume: 1500, time: 1642600676 },
  { open: 9.78, high: 10.59, low: 9.18, close: 9.51, volume: 2100, time: 1642687076 },
  { open: 9.51, high: 10.46, low: 9.10, close: 10.17, volume: 2400, time: 1642773476 },
  { open: 10.17, high: 10.96, low: 10.16, close: 10.47, volume: 3000, time: 1642859876 },
  { open: 10.47, high: 11.39, low: 10.40, close: 10.81, volume: 2800, time: 1642946276 },
  { open: 10.81, high: 11.60, low: 10.30, close: 10.75, volume: 3500, time: 1643032676 },
  { open: 10.75, high: 11.60, low: 10.49, close: 10.93, volume: 3700, time: 1643119076 },
  { open: 10.93, high: 11.53, low: 10.76, close: 10.96, volume: 4000, time: 1643205476 },
];
const Index = () => {
 
// const maxVolume = Math.max(...sampleData.map(d => d.quote_asset_volume));

  return (
    <>
    <MainContentHeader/>
    <div className="max-w-xl mx-auto mt-12 text-center p-4">
      <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
        Technical Analysis Stock Charts
      </h1>
      <p className="mt-2 text-gray-600 text-sm md:text-base">
        Search for a stock symbol to view an interactive chart with
        technical indicators, drawing tools, and comparison features.
      </p>

      <div className="mt-6 relative">
       {/* <SearchBox instanceId="chart" /> */}
            <SearchBox searchContext="chart" />
       
       
      </div>

      <p className="mt-4 text-sm text-gray-500">
        Examples:{" "}
        <a href="#" className="text-blue-600 hover:underline">NVDA</a>,{" "}
        <a href="#" className="text-blue-600 hover:underline">AAPL</a>,{" "}
        <a href="#" className="text-blue-600 hover:underline">SPY</a>,{" "}
        <a href="#" className="text-blue-600 hover:underline">QQQ</a>
      </p>
    </div>
    {/* <div className="min-h-screen bg-gray-100 flex flex-col items-center p-10">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">Candlestick Chart Example</h1>
      <div className="w-full max-w-6xl"> */}
        {/* <CandleStickChart klines={sampleData} maxVolume={maxVolume}  /> */}
        {/* <StockChart  symbol = {'USD/JPY'} interval = {'1min'} /> */}
                {/* <StockChart  data={dummyData} /> */}

      {/* </div> */}
    {/* </div> */}
    </>
  )
}

export default Index