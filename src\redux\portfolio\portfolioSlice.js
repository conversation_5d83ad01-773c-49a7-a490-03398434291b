import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "react-toastify";

// Async thunk to fetch all portfolios
export const fetchAllPortfolios = createAsyncThunk(
  "portfolio/fetchAllPortfolios",
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      const response = await fetch(`${backendURL}/api/portfolios/`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        credentials: "include",
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to fetch portfolios");
      }

      // Transform the API response to match the expected format for the Redux store
      const portfolios = Array.isArray(data.portfolios)
        ? data.portfolios.map((portfolio) => ({
            id: portfolio.id.toString(),
            name: portfolio.name,
            holdings: portfolio.holdings || [],
            performance: portfolio.performance || 0,
          }))
        : [];

      return portfolios;
    } catch (error) {
      return rejectWithValue(error.message || "Failed to fetch portfolios");
    }
  }
);

// Async thunk to fetch stock details with price and day gain information
export const fetchStockDetails = createAsyncThunk(
  "portfolio/fetchStockDetails",
  async (portfolioId, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}/holdings/stock-detail`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include",
        }
      );

      if (response.status === 404) {
        return {
          success: false,
          data: [],
          message: "Portfolio not found or no stock details available.",
        };
      }

      const data = await response.json();

      if (!data.success) {
        return rejectWithValue(
          data.message || "Failed to fetch stock details"
        );
      }

      return data;
    } catch (error) {
      return rejectWithValue(
        error.message || "Failed to fetch stock details"
      );
    }
  }
);

// Async thunk to fetch portfolio holdings with gain information
export const fetchPortfolioHoldings = createAsyncThunk(
  "portfolio/fetchHoldings",
  async (
    { portfolioId, gainType = "totalGain" },
    { rejectWithValue, getState }
  ) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Authentication required. Please log in.");
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      // Show loading toast
      const toastId = toast.loading("Fetching portfolio holdings...");

      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}/holdings?type=${gainType}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include",
        }
      );

      // If we get a 404, return empty holdings with a default message
      if (response.status === 404) {
        toast.update(toastId, {
          render: "Portfolio not found. Showing empty holdings.",
          type: "warning",
          isLoading: false,
          autoClose: 3000,
        });
        return {
          success: true,
          holdings: [],
          message: "Portfolio not found. Showing empty holdings.",
        };
      }

      const data = await response.json();

      if (!data.success) {
        toast.update(toastId, {
          render: data.message || "Failed to fetch holdings",
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
        return {
          success: false,
          holdings: [],
          message: data.message || "Failed to fetch holdings",
        };
      }

      // Update loading toast to success
      toast.update(toastId, {
        render: "Holdings fetched successfully",
        type: "success",
        isLoading: false,
        autoClose: 2000,
      });

      // Enrich holdings data with names from the Redux store if missing
      const state = getState();
      const allStocks = state.stocks.data || [];
      const portfolios = state.portfolio.portfolios || [];
      const currentPortfolio = portfolios.find((p) => p.id === portfolioId);

      // If we have holdings data, check each holding for a name
      if (data.holdings && Array.isArray(data.holdings)) {
        let missingNames = 0;

        data.holdings = data.holdings.map((holding) => {
          // If holding doesn't have a name, try to find it
          if (!holding.name || holding.name === "") {
            missingNames++;

            // First check in all stocks from the Redux store
            const stockInfo = allStocks.find(
              (s) => s.symbol === holding.symbol
            );
            if (stockInfo && stockInfo.name) {
              return { ...holding, name: stockInfo.name };
            }

            // If not found in stocks, check in the local portfolio holdings
            if (currentPortfolio && currentPortfolio.holdings) {
              const portfolioHolding = currentPortfolio.holdings.find(
                (h) => h.symbol === holding.symbol
              );
              if (portfolioHolding && portfolioHolding.name) {
                return { ...holding, name: portfolioHolding.name };
              }
            }

            // If still no name, use the symbol as a fallback
            return { ...holding, name: holding.symbol_code || holding.symbol };
          }
          return holding;
        });

        // Notify if names were missing and had to be derived
        if (missingNames > 0) {
          console.warn(`Fixed ${missingNames} holdings with missing names`);
        }
      }

      return data;
    } catch (error) {
      // Show error toast if not already shown
      toast.error(error.message || "Failed to fetch portfolio holdings");
      return rejectWithValue(
        error.message || "Failed to fetch portfolio holdings"
      );
    }
  }
);

// Async thunk to delete portfolio
export const deletePortfolioAsync = createAsyncThunk(
  "portfolio/deletePortfolioAsync",
  async (portfolioId, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include",
        }
      );

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to delete portfolio");
      }

      return portfolioId;
    } catch (error) {
      return rejectWithValue(error.message || "Failed to delete portfolio");
    }
  }
);

// Async thunk to delete a purchase record
export const deletePurchaseRecordAsync = createAsyncThunk(
  "portfolio/deletePurchaseRecordAsync",
  async ({ portfolioId, symbol, purchase_id }, { rejectWithValue }) => {
    // console.log("Deleting purchase record...");
    // console.log(portfolioId, symbol, purchase_id)

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      // console.log(`Deleting record: portfolioId=${portfolioId}, symbol=${symbol}, purchase_id=${purchase_id}`);
      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}/holdings/${symbol}/purchase/${purchase_id}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        let errorMessage = "Failed to delete purchase record";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error("Error parsing error response:", e);
        }
        throw new Error(errorMessage);
      }

      // Return the payload with the purchase_id to match the backend API response
      return {
        portfolioId,
        symbol,
        purchase_id,
      };
    } catch (error) {
      return rejectWithValue(
        error.message || "Failed to delete purchase record"
      );
    }
  }
);

// Async thunk for recording a new purchase for an existing stock
export const recordPurchaseAsync = createAsyncThunk(
  "portfolio/recordPurchaseAsync",
  async ({ portfolioId, symbol, purchaseData }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}/holdings/recordPurchase/${symbol}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            quantity: purchaseData.quantity,
            purchase_price: purchaseData.purchase_price,
            purchase_date: purchaseData.purchase_date,
          }),
          credentials: "include",
        }
      );

      if (!response.ok) {
        let errorMessage = "Failed to record purchase";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error("Error parsing error response:", e);
        }
        throw new Error(errorMessage);
      }

      // Return the payload with the symbol to match the backend API response
      return {
        portfolioId,
        symbol,
        success: true,
      };
    } catch (error) {
      return rejectWithValue(error.message || "Failed to record purchase");
    }
  }
);

// Async thunk for deleting all holdings of a specific stock
export const deleteAllHoldingsAsync = createAsyncThunk(
  "portfolio/deleteAllHoldingsAsync",
  async ({ portfolioId, symbol }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}/holdings/${symbol}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        let errorMessage = "Failed to delete all holdings";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error("Error parsing error response:", e);
        }
        throw new Error(errorMessage);
      }

      // Return the payload with the portfolioId and symbol
      return {
        portfolioId,
        symbol,
        success: true,
      };
    } catch (error) {
      return rejectWithValue(error.message || "Failed to delete all holdings");
    }
  }
);

extraReducers: (builder) => {
  builder
    .addCase(recordPurchaseAsync.pending, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(recordPurchaseAsync.fulfilled, (state, action) => {
      state.loading = false;
      // Refresh the holdings data after a successful purchase recording
      // The actual data will be fetched by the component
    })
    .addCase(recordPurchaseAsync.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload;
      toast.error(`Failed to record purchase: ${action.payload}`);
    });
};

// Async thunk to rename portfolio
export const renamePortfolioAsync = createAsyncThunk(
  "portfolio/renamePortfolioAsync",
  async ({ portfolioId, newName }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const backendURL =
        import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

      const response = await fetch(
        `${backendURL}/api/portfolios/${portfolioId}/rename`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ name: newName }),
          credentials: "include",
        }
      );

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to rename portfolio");
      }

      return { id: portfolioId, newName };
    } catch (error) {
      return rejectWithValue(error.message || "Failed to rename portfolio");
    }
  }
);

const portfolioSlice = createSlice({
  name: "portfolio",
  initialState: {
    portfolios: [], // Array of portfolios: { id, name, holdings: [] }
    activePortfolioId: null,
    holdingsData: null, // Will store the detailed holdings data from API
    loading: false,
    error: null,
    stockDetails: {},
    loadingStockDetails: false,
    stockDetailsError: null,
  },
  reducers: {
    createPortfolio: (state, action) => {
      const newPortfolio = {
        id: action.payload.id, // Use the ID provided in the payload
        name: action.payload.name,
        holdings: [],
      };
      state.portfolios.push(newPortfolio);
      state.activePortfolioId = newPortfolio.id;
    },

    addInvestment: (state, action) => {
      const { portfolioId, investment } = action.payload;
      const portfolio = state.portfolios.find((p) => p.id === portfolioId);
      if (portfolio) {
        portfolio.holdings.push(investment);
      }
    },
    renamePortfolio: (state, action) => {
      const { id, newName } = action.payload;
      const portfolio = state.portfolios.find((p) => p.id === id);
      if (portfolio) {
        portfolio.name = newName;
      }
    },
    deletePortfolio: (state, action) => {
      state.portfolios = state.portfolios.filter(
        (p) => p.id !== action.payload
      );
      if (state.activePortfolioId === action.payload) {
        state.activePortfolioId = null;
      }
    },
    deleteStockFromPortfolio: (state, action) => {
      const { portfolioId, symbol } = action.payload;
      const portfolio = state.portfolios.find((p) => p.id === portfolioId);
      if (portfolio) {
        portfolio.holdings = portfolio.holdings.filter(
          (h) => h.symbol !== symbol
        );
      }
    },
    setActivePortfolio: (state, action) => {
      state.activePortfolioId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all portfolios
      .addCase(fetchAllPortfolios.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllPortfolios.fulfilled, (state, action) => {
        state.loading = false;
        state.portfolios = action.payload;
      })
      .addCase(fetchAllPortfolios.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete portfolio
      .addCase(deletePortfolioAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePortfolioAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.portfolios = state.portfolios.filter(
          (p) => p.id !== action.payload
        );
        if (state.activePortfolioId === action.payload) {
          state.activePortfolioId = null;
        }
      })
      .addCase(deletePortfolioAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Rename portfolio
      .addCase(renamePortfolioAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(renamePortfolioAsync.fulfilled, (state, action) => {
        state.loading = false;
        const { id, newName } = action.payload;
        const portfolio = state.portfolios.find((p) => p.id === id);
        if (portfolio) {
          portfolio.name = newName;
        }
      })
      .addCase(renamePortfolioAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch portfolio holdings
      .addCase(fetchPortfolioHoldings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPortfolioHoldings.fulfilled, (state, action) => {
        state.loading = false;
        state.holdingsData = action.payload;
      })
      .addCase(fetchPortfolioHoldings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error(`Failed to load Investments: ${action.payload}`);
      })
      
      // Fetch stock details with price and day gain information
      .addCase(fetchStockDetails.pending, (state) => {
        state.loadingStockDetails = true;
        state.stockDetailsError = null;
      })
      .addCase(fetchStockDetails.fulfilled, (state, action) => {
        state.loadingStockDetails = false;
        if (action.payload && action.payload.success && Array.isArray(action.payload.data)) {
          // Convert array to object with symbol as key for easier lookup
          const detailsObj = {};
          action.payload.data.forEach(stock => {
            detailsObj[stock.symbol] = stock;
          });
          state.stockDetails = detailsObj;
        }
      })
      .addCase(fetchStockDetails.rejected, (state, action) => {
        state.loadingStockDetails = false;
        state.stockDetailsError = action.payload;
      })

      // Delete purchase record
      .addCase(deletePurchaseRecordAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePurchaseRecordAsync.fulfilled, (state, action) => {
        state.loading = false;
        // If we have holdings data and it contains the portfolio with the deleted purchase
        if (state.holdingsData && state.holdingsData.holdings) {
          // Find the holding with the matching symbol
          const holdingIndex = state.holdingsData.holdings.findIndex(
            (h) => h.symbol === action.payload.symbol
          );

          if (
            holdingIndex !== -1 &&
            state.holdingsData.holdings[holdingIndex].details
          ) {
            // Filter out the deleted purchase record based on the exact API response structure
            // Your API response shows that details array contains objects with 'purchase_id' property
            state.holdingsData.holdings[holdingIndex].details =
              state.holdingsData.holdings[holdingIndex].details.filter(
                (detail) => {
                  return detail.purchase_id !== action.payload.purchase_id;
                }
              );

            // If we removed all details for this holding, we should recalculate totals or remove the holding
            if (
              state.holdingsData.holdings[holdingIndex].details.length === 0
            ) {
              // Option 1: Remove the holding entirely
              state.holdingsData.holdings = state.holdingsData.holdings.filter(
                (h) => h.symbol !== action.payload.symbol
              );
            }
          }
        }
      })
      .addCase(deletePurchaseRecordAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error(`Failed to delete purchase record: ${action.payload}`);
      })

      // Delete all holdings of a specific stock
      .addCase(deleteAllHoldingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAllHoldingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // If we have holdings data, remove the stock from the holdings
        if (state.holdingsData && state.holdingsData.holdings) {
          state.holdingsData.holdings = state.holdingsData.holdings.filter(
            (h) => h.symbol !== action.payload.symbol
          );
        }
      })
      .addCase(deleteAllHoldingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error(`Failed to delete all holdings: ${action.payload}`);
      });
  },
});

export const {
  createPortfolio,
  addInvestment,
  setActivePortfolio,
  renamePortfolio,
  deletePortfolio,
  deleteStockFromPortfolio,
} = portfolioSlice.actions;

export default portfolioSlice.reducer;
