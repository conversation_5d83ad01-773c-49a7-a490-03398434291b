import React from "react";
import { Table, Checkbox } from "antd";

export default function Performance({ data, selectedRowKeys, onChange }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "company" },
    { title: "Price", dataIndex: "price" },
    {
      title: "1D Change",
      dataIndex: "dayChange",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
    {
      title: "1W Change",
      dataIndex: "weekChange",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
    {
      title: "1M Change",
      dataIndex: "monthChange",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
    {
      title: "YTD Change",
      dataIndex: "ytdChange",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
    {
      title: "1Y Change",
      dataIndex: "yearChange",
      render: (txt) => (
        <span className={txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt}
        </span>
      ),
    },
  ];

  return <Table columns={columns} dataSource={data} pagination={false} />;
}
