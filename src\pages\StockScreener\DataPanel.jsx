// src/pages/StockScreener/DataPanel.jsx
import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addFilter, removeFilter, clearFilters } from '../../redux/filter/filterSlice';
import FilterModal from './FilterModal';
import { Dropdown, AutoComplete, Button, Space, Select } from 'antd';
import { PlusOutlined, DownOutlined } from '@ant-design/icons';
import { filterCategories } from './FiltersData';

const { Option } = Select;

const DataPanel = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [searchTerm, setSearchTerm]     = useState('');
  const [options, setOptions]           = useState([]);
  const selectedFilters = useSelector((state) => state.filters.selectedFilters);
  const dispatch        = useDispatch();

  // State for the three new dropdowns
  const [marketCapCond, setMarketCapCond]   = useState('Any');
  const [industryCond, setIndustryCond]     = useState('Any');
  const [stockPriceCond, setStockPriceCond] = useState('Any');

  // Build flat list of all filters
  const allFilters = filterCategories.flatMap((c) => c.filters);

  // Quick-add search logic
  const handleSearch = (value) => {
    setSearchTerm(value);
    if (value.length >= 3) {
      setOptions(
        allFilters
          .filter((f) => f.toLowerCase().includes(value.toLowerCase()))
          .map((f) => ({ value: f }))
      );
    } else {
      setOptions([]);
    }
  };

  const handleSelect = (value) => {
    dispatch(addFilter(value));
    setSearchTerm('');
    setOptions([]);
  };

  // Dropdown panel content rendered using dropdownRender
  const dropdownContent = (
    <div className="p-4 bg-white rounded shadow" style={{ width: 400 }}>
      <Space style={{ width: '100%', marginBottom: 12 }} align="start">
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={() => setModalVisible(true)}
        >
          Add Filter
        </Button>
        <AutoComplete
          options={options}
          onSelect={handleSelect}
          onSearch={handleSearch}
          value={searchTerm}
          placeholder="Search filters..."
          allowClear
          style={{ flex: 1 }}
        />
      </Space>
    </div>
  );

  return (
    <div className="max-w-5xl mx-auto p-4 flex flex-col gap-4">
      {/* Filters dropdown trigger */}
      <Dropdown
        dropdownRender={() => dropdownContent}
        trigger={['click']}
        placement="bottomLeft"
      >
        <Button>
          Filters <DownOutlined />
        </Button>
      </Dropdown>

      {/* Selected Filters as Chips */}
      {selectedFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedFilters.map((filter) => (
            <div
              key={filter}
              className="flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full"
            >
              <span className="mr-2">{filter}</span>
              <PlusOutlined
                rotate={45}
                className="cursor-pointer"
                onClick={() => dispatch(removeFilter(filter))}
              />
            </div>
          ))}
          <Button type="link" danger onClick={() => dispatch(clearFilters())}>
            Clear All
          </Button>
        </div>
      )}

      {/* ===== Inline dropdowns ===== */}
      <div className="flex flex-wrap gap-8 mt-4">
        <div className="flex items-center space-x-2">
          <span className="font-medium">Market Cap:</span>
          <Select
            value={marketCapCond}
            onChange={setMarketCapCond}
            style={{ width: 140 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Less Than">Less Than</Option>
            <Option value="More Than">More Than</Option>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="font-medium">Industry:</span>
          <Select
            value={industryCond}
            onChange={setIndustryCond}
            style={{ width: 180 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Technology">Technology</Option>
            <Option value="Financials">Financials</Option>
            <Option value="Healthcare">Healthcare</Option>
            <Option value="Consumer Goods">Consumer Goods</Option>
            {/* add more industries as needed */}
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="font-medium">Stock Price:</span>
          <Select
            value={stockPriceCond}
            onChange={setStockPriceCond}
            style={{ width: 140 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Less Than">Less Than</Option>
            <Option value="More Than">More Than</Option>
          </Select>
        </div>
      </div>

      {/* Bulk-select modal */}
      <FilterModal visible={modalVisible} onClose={() => setModalVisible(false)} />
    </div>
  );
};

export default DataPanel;
