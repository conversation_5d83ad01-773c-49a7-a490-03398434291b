import { useState, useEffect } from "react";
import { Tabs, Pagination } from "antd";
import { useSelector, useDispatch } from "react-redux";
import Overview from "./Tabs/Overview";
import Performance from "./Tabs/Performance";
import Earnings from "./Tabs/Earnings";
import { loadScreenerOverview } from "../../redux/screener/screenerSlice";

export default function StockScreener() {
  const dispatch = useDispatch();
  const { overview: stocks, status } = useSelector((state) => state.screener);

  const [processedData, setProcessedData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  useEffect(() => {
    dispatch(loadScreenerOverview());
  }, [dispatch]);

  useEffect(() => {
    const transformed = stocks.map((stock, idx) => ({
      key: stock.symbol || idx,
      symbol: stock.symbol || "N/A",
      company: stock.name || "N/A",
      price: stock.price ? `${stock.price} SAR` : "N/A",
      dayChange: stock.change ? `${stock.change}%` : "0%",
      divYield: stock.Div_yield ? `${stock.Div_yield}%` : "0%",
      peRatio: stock.pe_ratio || "N/A", // if added later
      high52: stock._52High ? `${stock._52High} SAR` : "N/A",
      low52: stock._52Low ? `${stock._52Low} SAR` : "N/A",
      marketCap: stock.Market_Cap ? `${stock.Market_Cap} SAR` : "N/A",
      weekChange: "0%",
      monthChange: "0%",
      ytdChange: "0%",
      yearChange: "0%",
      eps: "N/A",
      revGrowth: "0%",
      profitGrowth: "0%",
    }));
    setProcessedData(transformed);
  }, [stocks]);

  const currentData = processedData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const items = [
    {
      key: "overview",
      label: "Overview",
      children: (
        <Overview
          data={currentData}
          selectedRowKeys={selectedRowKeys}
          onChange={setSelectedRowKeys}
        />
      ),
    },
    {
      key: "performance",
      label: "Performance",
      children: (
        <Performance
          data={currentData}
          selectedRowKeys={selectedRowKeys}
          onChange={setSelectedRowKeys}
        />
      ),
    },
    {
      key: "earnings",
      label: "Earnings",
      children: (
        <Earnings
          data={currentData}
          selectedRowKeys={selectedRowKeys}
          onChange={setSelectedRowKeys}
        />
      ),
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-900 dark:text-white p-4 rounded-lg shadow-md">
      <Tabs activeKey={activeTab} onChange={setActiveTab} items={items} />

      <div className="mt-4 flex justify-center">
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={processedData.length}
          onChange={setCurrentPage}
          showSizeChanger={false}
        />
      </div>
    </div>
  );
}
