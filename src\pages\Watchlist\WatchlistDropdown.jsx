import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  loadWatchlists,
  createNewWatchlist,
  renameExistingWatchlist,
  deleteExistingWatchlist
} from '../../redux/watchlist/watchlistSlice';
import { Pencil, Trash, Copy, Check } from 'lucide-react';

export default function WatchlistDropdown({ onSelect }) {
  const dispatch = useDispatch();
  const { items, loading } = useSelector(state => state.watchlist);
  const [open, setOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [editingIndex, setEditingIndex] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [newName, setNewName] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  useEffect(() => { dispatch(loadWatchlists()); }, [dispatch]);
  useEffect(() => { if(items.length) onSelect(items[activeIndex]); }, [items, activeIndex]);

  const handleAdd = () => { setIsAdding(true); setEditValue(''); };
  const saveNew = () => { dispatch(createNewWatchlist(newName)); setIsAdding(false); };
  const handleEdit = idx => { setEditingIndex(idx); setEditValue(items[idx].name); };
  const saveEdit = idx => {
    dispatch(renameExistingWatchlist({ id: items[idx].id, newName: editValue }));
    setEditingIndex(null);
  };
  const handleDelete = idx => dispatch(deleteExistingWatchlist(items[idx].id));

  if (loading) return <div>Loading...</div>;

  return (
    <div className="relative inline-block text-left">
      <button onClick={()=>setOpen(!open)} className="border px-4 py-2 rounded-md">
        {items[activeIndex]?.name || 'No Watchlist'} ▼
      </button>
      {open && (
        <div className="absolute z-10 mt-2 w-64 bg-white border rounded shadow max-h-80 overflow-y-auto">
          {items.map((w, i) => (
            <div key={w.id} className={`px-3 py-2 flex justify-between ${i===activeIndex?'bg-gray-100':''}`}>
              {editingIndex===i ? (
                <input value={editValue} onChange={e=>setEditValue(e.target.value)} className="border p-1 rounded w-full mr-2"/>
              ) : (
                <span className="flex-1 cursor-pointer" onClick={()=>{setActiveIndex(i); onSelect(w);}}>{w.name}</span>
              )}
              {editingIndex===i ? (
                <Check onClick={()=>saveEdit(i)} className="cursor-pointer" />
              ) : (
                <> <Pencil onClick={()=>handleEdit(i)} /> <Trash onClick={()=>handleDelete(i)} /> </>
              )}
            </div>
          ))}
          <div onClick={handleAdd} className="px-3 py-2 border-t cursor-pointer">+ Add Watchlist</div>
          {isAdding && (
            <div className="p-2">
              <input value={newName} onChange={e=>setNewName(e.target.value)} placeholder="Name..." className="border p-1 rounded w-full"/>
              <button onClick={saveNew} className="mt-1 w-full py-1 bg-green-600 text-white rounded">Save</button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}