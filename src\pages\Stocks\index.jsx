// src/pages/StocksIndex/Index.jsx

import  { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Table, Dropdown, Menu, Button } from "antd";
import { DownOutlined, EditOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

import MainContentHeader from "../../components/common/MainContentHeader";
import MarketSelector from "../../components/common/MarketSelector";

import {
  fetchTasi,
  fetchNumo,
  fetchAllMarket,
} from "../../redux/stocks/stocksActions";

// Industry dropdown menu items
const IndustriesMenu = (
  <Menu>
    <Menu.Item key="tech">Technology</Menu.Item>
    <Menu.Item key="fin">Financials</Menu.Item>
    <Menu.Item key="health">Healthcare</Menu.Item>
    <Menu.Item key="cons">Consumer Goods</Menu.Item>
    <Menu.Item key="energy">Energy</Menu.Item>
  </Menu>
);

const Index = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { data, status } = useSelector((state) => state.stocks);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize]     = useState(8);

  // Load & persist selected market
  const [selectedMarket, setSelectedMarket] = useState(
    () => localStorage.getItem("selectedMarket") || "all"
  );

  useEffect(() => {
    localStorage.setItem("selectedMarket", selectedMarket);

    switch (selectedMarket) {
      case "tasi":
        dispatch(fetchTasi());
        break;
      case "numo":
        dispatch(fetchNumo());
        break;
      default:
        dispatch(fetchAllMarket());
    }
  }, [selectedMarket, dispatch]);

  const columns = [
    { title: "Symbol", dataIndex: "symbol", key: "symbol" },
    { title: "Company Name", dataIndex: "name", key: "name" },
    { title: "Stock Price", dataIndex: "price", key: "price" },
    { title: "1D Change (%)", dataIndex: "change", key: "change" },
    { title: "Div Yield (%)", dataIndex: "div_yield", key: "div_yield" },
    { title: "P/E Ratio", dataIndex: "pe_ratio", key: "pe_ratio" },
    { title: "52-wk High", dataIndex: "high", key: "high" },
    { title: "52-wk Low", dataIndex: "low", key: "low" },
    { title: "Market Cap", dataIndex: "market_cap", key: "market_cap" },
  ];

  return (
    <div className="stocks-table bg-white dark:bg-gray-900 dark:text-white p-4">
      <MainContentHeader />

      {/* Market selector */}
      <div className="w-40 border border-black dark:border-white text-center mx-auto my-4">
        <MarketSelector
          value={selectedMarket}
          onChange={(val) => setSelectedMarket(val)}
        />
      </div>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="symbol"
        loading={status === "loading"}
        pagination={{
          pageSize,
          current: currentPage,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size);
          },
        }}
        scroll={{ x: "max-content" }}
        onRow={(record) => ({
          onClick: () => navigate(`/stocks/${record.symbol}`),
        })}
        bordered
        title={() => (
          <div
            className="flex justify-between items-center px-2"
            style={{ borderBottom: "1px solid #f0f0f0" }}
          >
            {/* Left: Industry dropdown */}
            <Dropdown overlay={IndustriesMenu} placement="bottomLeft">
              <Button
                className="text-lg font-semibold px-2 py-1"
                style={{
                  border: "1px solid #d9d9d9",
                  borderBottom: "none",
                  borderTopLeftRadius: 4,
                  borderTopRightRadius: 4,
                  background: "white",
                }}
              >
                Industry <DownOutlined />
              </Button>
            </Dropdown>

            {/* Right: Edit button */}
            <Button icon={<EditOutlined />} className="border px-3 py-1">
              Edit
            </Button>
          </div>
        )}
      />
    </div>
  );
};

export default Index;
