import { Table } from "antd";
import MarketSelector from "../../components/common/MarketSelector";
import MarketSummaryCharts from "./MarketSummaryCharts";
import { useTranslation } from "react-i18next";
import SearchBox from "../../components/common/SearchBox";
const gainersData = [
  {
    key: "1",
    symbol: "QVCGB",
    name: "QVC Group",
    price: "$8.04",
    change: "302.00%",
  },
  {
    key: "2",
    symbol: "WAFU",
    name: "Wah Fu Education Group",
    price: "$3.73",
    change: "108.38%",
  },
  {
    key: "3",
    symbol: "BREA",
    name: "Brera Holdings",
    price: "$1.24",
    change: "96.42%",
  },
  {
    key: "4",
    symbol: "FMTO",
    name: "Femto Technologies",
    price: "$6.63",
    change: "85.37%",
  },
  {
    key: "5",
    symbol: "MBRX",
    name: "Moleculin Biotech",
    price: "$1.83",
    change: "71.03%",
  },
  {
    key: "6",
    symbol: "MFI",
    name: "mF International",
    price: "$1.02",
    change: "70.09%",
  },
  {
    key: "7",
    symbol: "PMTS",
    name: "CPI Card Group",
    price: "$45.00",
    change: "45.16%",
  },
  {
    key: "8",
    symbol: "FEAM",
    name: "5E Advanced Materials",
    price: "$5.37",
    change: "30.34%",
  },
  { key: "9", symbol: "DMN", name: "Damon", price: "$0.39", change: "28.60%" },
  {
    key: "10",
    symbol: "BOWN",
    name: "Bowen Acquisition",
    price: "$4.50",
    change: "27.12%",
  },
];

const losersData = [
  {
    key: "1",
    symbol: "KTCC",
    name: "Key Tronic",
    price: "$2.00",
    change: "-35.48%",
  },
  {
    key: "2",
    symbol: "YYAI",
    name: "Connexa Sports Technologies",
    price: "$0.84",
    change: "-33.74%",
  },
  {
    key: "3",
    symbol: "STEC",
    name: "Santech Holdings",
    price: "$1.11",
    change: "-31.90%",
  },
  {
    key: "4",
    symbol: "OOMA",
    name: "Ooma, Inc.",
    price: "$11.21",
    change: "-24.05%",
  },
  {
    key: "5",
    symbol: "ATPC",
    name: "Agape ATP",
    price: "$1.23",
    change: "-23.13%",
  },
  {
    key: "6",
    symbol: "RETO",
    name: "ReTo Eco-Solutions",
    price: "$0.45",
    change: "-23.03%",
  },
  {
    key: "7",
    symbol: "ELUT",
    name: "Elutia",
    price: "$2.50",
    change: "-22.36%",
  },
  {
    key: "8",
    symbol: "CHGG",
    name: "Chegg",
    price: "$1.22",
    change: "-21.79%",
  },
  {
    key: "9",
    symbol: "FGEN",
    name: "FibroGen",
    price: "$0.46",
    change: "-20.72%",
  },
  {
    key: "10",
    symbol: "CLIK",
    name: "Click Holdings",
    price: "$1.16",
    change: "-19.44%",
  },
];

const Homepage = () => {
  const { t } = useTranslation();

  const columns = [
    {
      title: t("table.symbol"),
      dataIndex: "symbol",
      key: "symbol",
      render: (text) => <a href="#">{text}</a>,
    },
    { title: t("table.name"), dataIndex: "name", key: "name" },
    { title: t("table.price"), dataIndex: "price", key: "price" },
    {
      title: t("table.change"),
      dataIndex: "change",
      key: "change",
      render: (text) => (
        <span
          className={text.includes("-") ? "text-red-500" : "text-green-500"}
        >
          {text}
        </span>
      ),
    },
  ];

  return (
    <div className="container mx-auto p-4 bg-white dark:bg-gray-900 dark:text-white">
      {/* Stock Indexes */}
      <div className="flex justify-center items-center">
        <MarketSummaryCharts />
      </div>

      {/* Search Bar */}
      <div className="text-center my-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {t("home.search_title")}
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          {t("home.search_description")}
        </p>
        {/* <input
          type="text"
          placeholder={t("home.search_placeholder")}
          className="w-full md:w-1/2 p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded mt-2"
        /> */}
     {/* <SearchBox instanceId="homepage" /> */}
     <SearchBox searchContext="homepage" />
     
      </div>

      {/* Buttons */}
      <div className="w-40 border border-black dark:border-white text-center mx-auto mb-3 bg-white dark:bg-gray-800">
        <MarketSelector />
      </div>

      {/* Top Gainers & Losers */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 text-center bg-white dark:bg-gray-800 shadow rounded">
          <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">
            {t("home.gainers")}
          </h3>
          <p className="text-gray-500 dark:text-gray-300 text-sm mb-4">
            {t("home.updated")}
          </p>
          <Table
            columns={columns}
            dataSource={gainersData}
            pagination={false}
            size="small"
            bordered
            className="dark:text-white"
          />
        </div>

        <div className="p-4 text-center bg-white dark:bg-gray-800 shadow rounded">
          <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">
            {t("home.losers")}
          </h3>
          <p className="text-gray-500 dark:text-gray-300 text-sm mb-4">
            {t("home.updated")}
          </p>
          <Table
            columns={columns}
            dataSource={losersData}
            pagination={false}
            size="small"
            bordered
            className="dark:text-white"
          />
        </div>
      </div>

      {/* Market News & IPOs */}
    </div>
  );
};

export default Homepage;
