// src/pages/Watchlist/WatchlistTable.jsx
import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Button, Modal, Input, Checkbox, Table, Dropdown, Menu } from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  MoreOutlined,
} from "@ant-design/icons";

import {
  removeStock,
  loadWatchlists,
  addCustomTab,
  setCustomTabFilters,
  deleteCustomTab,
  clearAllCustomTabs,
} from "../../redux/watchlist/watchlistSlice";
import WatchlistDropdown from "./WatchlistDropdown";

import Overview from "./Tabs/Overview";
import Performance from "./Tabs/Performance";
import Earnings from "./Tabs/Earnings";

import { filterCategories } from "./FiltersData";

export default function WatchlistTable() {
  const dispatch = useDispatch();
  const { items: watchlists, customTabs } = useSelector((state) => state.watchlist);

  const [selectedWatchlist, setSelectedWatchlist] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [isEditing, setIsEditing] = useState(false);

  // Modal state
  const [isNameModalVisible, setIsNameModalVisible] = useState(false);
  const [newTabName, setNewTabName] = useState("");
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [pendingTabId, setPendingTabId] = useState(null);

  useEffect(() => {
    dispatch(loadWatchlists());
  }, [dispatch]);

  useEffect(() => {
    if (watchlists.length && !selectedWatchlist) {
      setSelectedWatchlist(watchlists[0]);
    }
  }, [watchlists, selectedWatchlist]);

  const onSelectWatchlist = (wl) => {
    setSelectedWatchlist(wl);
    setSelectedRowKeys([]);
    setIsEditing(false);
    setActiveTab("overview");
  };

  const onDeleteStocks = () => {
    if (!selectedWatchlist || !selectedRowKeys.length) return;
    selectedRowKeys.forEach((symbol) =>
      dispatch(removeStock({ watchlistId: selectedWatchlist.id, symbol }))
    );
    setSelectedRowKeys([]);
  };

  const buildData = () =>
    (selectedWatchlist?.stocks || []).map((symbol) => ({
      key: symbol,
      symbol,
    }));

  const commonProps = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    rowSelectionEnabled: isEditing,
  };

  const renderTabContent = () => {
    if (activeTab === "overview")
      return <Overview data={buildData()} {...commonProps} />;
    if (activeTab === "performance")
      return <Performance data={buildData()} {...commonProps} />;
    if (activeTab === "earnings")
      return <Earnings data={buildData()} {...commonProps} />;

    const custom = customTabs.find((t) => t.name === activeTab);
    if (custom) {
      const columns = [
        ...(isEditing
          ? [
              {
                title: "",
                dataIndex: "key",
                render: (key) => (
                  <Checkbox
                    checked={selectedRowKeys.includes(key)}
                    onChange={() =>
                      setSelectedRowKeys((prev) =>
                        prev.includes(key)
                          ? prev.filter((k) => k !== key)
                          : [...prev, key]
                      )
                    }
                  />
                ),
              },
            ]
          : []),
        { title: "Symbol", dataIndex: "symbol" },
        ...custom.filters.map((f) => ({ title: f, dataIndex: f })),
      ];
      return (
        <Table
          columns={columns}
          dataSource={buildData()}
          pagination={false}
          rowKey="key"
        />
      );
    }

    return null;
  };

  const handleAddTabClick = () => {
    setIsNameModalVisible(true);
  };

  const saveNewTabName = () => {
    const id = Date.now();
    dispatch(addCustomTab({ id, name: newTabName.trim() }));
    setPendingTabId(id);
    setIsNameModalVisible(false);
    setNewTabName("");
    setIsFilterModalVisible(true);
  };

  const handleEditViewClick = () => {
    const tab = customTabs.find((t) => t.name === activeTab);
    if (!tab) return;
    setPendingTabId(tab.id);
    setSelectedFilters(tab.filters);
    setIsFilterModalVisible(true);
  };

  const saveFilters = () => {
    dispatch(
      setCustomTabFilters({
        id: pendingTabId,
        filters: selectedFilters,
      })
    );
    setIsFilterModalVisible(false);
    setSelectedFilters([]);
    const updated = customTabs.find((t) => t.id === pendingTabId);
    if (updated) setActiveTab(updated.name);
  };

  // Options Handlers
  const handleResetCurrent = () => {
    const current = customTabs.find((t) => t.name === activeTab);
    if (!current) return;
    dispatch(deleteCustomTab(current.id));
    setActiveTab("overview");
  };

  const handleResetAll = () => {
    dispatch(clearAllCustomTabs());
    setActiveTab("overview");
  };

  const isCustomTabActive = !!customTabs.find((t) => t.name === activeTab);

  const optionsMenu = (
    <Menu>
      <Menu.Item key="reset-current" onClick={handleResetCurrent}>Reset Current View</Menu.Item>
      <Menu.Item  key="reset-all" onClick={handleResetAll}>Reset All Views</Menu.Item>
    </Menu>
  );

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center space-x-4 mb-4">
        <WatchlistDropdown onSelect={onSelectWatchlist} />

        <div className="flex space-x-2">
          {["overview", "performance", "earnings", ...customTabs.map((t) => t.name)].map((tab) => (
            <button
              key={tab}
              onClick={() => {
                setActiveTab(tab);
                setIsEditing(false);
                setSelectedRowKeys([]);
              }}
              className={`px-3 py-1 rounded ${
                activeTab === tab ? "bg-black text-white" : "border"
              }`}
            >
              {tab}
            </button>
          ))}

          <Button icon={<PlusOutlined />} onClick={handleAddTabClick}>
            Add +
          </Button>

          {isCustomTabActive && (
            <>
              <Button icon={<EditOutlined />} onClick={handleEditViewClick}>
                Edit View
              </Button>
              <Dropdown overlay={optionsMenu} placement="bottomRight">
                <Button icon={<MoreOutlined />} />
              </Dropdown>
            </>
          )}
        </div>

        <div className="flex-1" />

        <Button
          icon={<EditOutlined />}
          onClick={() => {
            setIsEditing((e) => {
              if (e) setSelectedRowKeys([]);
              return !e;
            });
          }}
        >
          Edit
        </Button>
        {isEditing && selectedRowKeys.length > 0 && (
          <Button danger icon={<DeleteOutlined />} onClick={onDeleteStocks}>
            Delete
          </Button>
        )}
      </div>

      {renderTabContent()}

      {/* New Tab Name */}
      <Modal
        title="Create New Tab"
        open={isNameModalVisible}
        onCancel={() => setIsNameModalVisible(false)}
        onOk={saveNewTabName}
      >
        <Input
          placeholder="Enter tab name"
          value={newTabName}
          onChange={(e) => setNewTabName(e.target.value)}
        />
      </Modal>

      {/* Filter Selection */}
      <Modal
        title="Select Filters"
        open={isFilterModalVisible}
        onCancel={() => setIsFilterModalVisible(false)}
        onOk={saveFilters}
        width={600}
      >
        {filterCategories.map((cat) => (
          <div key={cat.title} className="mb-4">
            <strong>{cat.title}</strong>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {cat.filters.map((filter) => (
                <Checkbox
                  key={filter}
                  checked={selectedFilters.includes(filter)}
                  onChange={(e) => {
                    setSelectedFilters((prev) =>
                      e.target.checked
                        ? [...prev, filter]
                        : prev.filter((f) => f !== filter)
                    );
                  }}
                >
                  {filter}
                </Checkbox>
              ))}
            </div>
          </div>
        ))}
      </Modal>
    </div>
  );
}