import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { fetchStockScreenerOverview } from "../../redux/screener/screenerService";

export const loadScreenerOverview = createAsyncThunk(
  "screener/loadOverview",
  async () => {
    const data = await fetchStockScreenerOverview();
    return data;
  }
);

const screenerSlice = createSlice({
  name: "screener",
  initialState: {
    overview: [],
    status: "idle",
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(loadScreenerOverview.pending, (state) => {
        state.status = "loading";
      })
      .addCase(loadScreenerOverview.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.overview = action.payload;
      })
      .addCase(loadScreenerOverview.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });
  },
});

export default screenerSlice.reducer;
