import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import PropTypes from "prop-types";
import {
  createPortfolio,
  fetchAllPortfolios,
} from "../../redux/portfolio/portfolioSlice";
import { X } from "lucide-react";
// import axios from "axios";
import { toast } from "react-toastify";

const backendURL = import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

// Function to get the auth token
const getAuthToken = () => {
  // Try to get from localStorage
  const localToken = localStorage.getItem("token");
  if (localToken) {
    // console.log("Found token in localStorage");
    return localToken;
  }

  // // If not in localStorage, check sessionStorage
  // const sessionToken = sessionStorage.getItem("token");
  // if (sessionToken) {
  //   console.log("Found token in sessionStorage");
  //   return sessionToken;
  // }

  // console.log("No token found in storage");
  return null;
};

const PortfolioModal = ({ isOpen, onClose }) => {
  const [portfolioName, setPortfolioName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  const handleSave = async () => {
    if (portfolioName.trim()) {
      try {
        setIsLoading(true);
        setError(null);

        // Show loading toast
        const toastId = toast.loading("Creating portfolio...");

        // Get token from localStorage
        const token = getAuthToken();

        if (!token) {
          toast.update(toastId, {
            render: "Please log in to create a portfolio",
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
          setError("Please log in to create a portfolio");
          setIsLoading(false);
          return;
        }

        // Direct API call using fetch
        try {
          const response = await fetch(`${backendURL}/api/portfolios/create`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ portfolio_name: portfolioName.trim() }),
            credentials: "include",
          });

          const data = await response.json();

          if (data.success) {
            toast.update(toastId, {
              render: `Portfolio "${portfolioName}" created successfully!`,
              type: "success",
              isLoading: false,
              autoClose: 2000,
            });

            const newPortfolio = {
              id: data.portfolio.id.toString(),
              name: data.portfolio.name,
              holdings: [],
            };

            // Update Redux store with the new portfolio
            dispatch(createPortfolio(newPortfolio));

            // Refresh the portfolios list from the API
            dispatch(fetchAllPortfolios());

            onClose();
            navigate(`/portfolio/${newPortfolio.id}`);
          } else {
            toast.update(toastId, {
              render: "Please sign in to create a portfolio",

              type: "error",
              isLoading: false,
              autoClose: 3000,
            });
            setError("Please log in to create a portfolio");
          }
        } catch (error) {
          console.error("Portfolio creation error:", error);
          toast.update(toastId, {
            render: "Please log in to create a portfolio",

            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
          throw error;
        }
      } catch (err) {
        console.error("Portfolio creation error:", err);

        if (err.message) {
          toast.error(`Error: ${err.message}`);
          setError(`Error: ${err.message}`);
        } else {
          toast.error("An unknown error occurred");
          setError("An unknown error occurred");
        }
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-[1000]"
      style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
    >
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl w-96 max-w-[90%] transform transition-all duration-300 scale-100 opacity-100 border border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center mb-5">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
            Create a New Portfolio
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <input
          type="text"
          value={portfolioName}
          onChange={(e) => setPortfolioName(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Enter portfolio name"
          className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all"
          autoFocus
          disabled={isLoading}
        />

        {error && <p className="text-red-500 text-sm mt-2">{error}</p>}

        <div className="flex justify-end mt-5 space-x-3">
          <button
            onClick={onClose}
            className="px-5 py-2 text-gray-600 dark:text-gray-300 font-medium bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all"
            disabled={isLoading}
          >
            Cancel
          </button>

          <button
            onClick={handleSave}
            className="px-5 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-all flex items-center justify-center min-w-[80px]"
            disabled={!portfolioName.trim() || isLoading}
          >
            {isLoading ? (
              <span className="inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></span>
            ) : null}
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

PortfolioModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default PortfolioModal;
